# Using Custom Classes in Pyleet

This guide shows you how to use custom classes other than TreeNode and ListNode in Pyleet's test framework.

## Overview

Pyleet supports any custom class through its extensible deserializer system. You can register deserializers for your own classes and use them in test cases with the format `{"ClassName": data}`.

## Step-by-Step Process

### 1. Define Your Custom Class

```python
class Point:
    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y
    
    def __eq__(self, other):  # Important for test comparisons
        if not isinstance(other, Point):
            return False
        return self.x == other.x and self.y == other.y
    
    def __repr__(self):  # Helpful for debugging
        return f"Point({self.x}, {self.y})"
```

### 2. Define a Deserializer Function

```python
def list_to_point(data):
    """
    Convert list to Point.
    Format: [x, y]
    Example: [3, 4] -> Point(3, 4)
    """
    if not data or len(data) != 2:
        return None
    return Point(data[0], data[1])
```

### 3. Register Your Deserializer

```python
from pyleet import register_deserializer

register_deserializer("Point", list_to_point)
```

### 4. Use in Test Cases

```json
[
  {
    "input": [{"Point": [3, 4]}],
    "expected": 5.0
  },
  {
    "input": [{"Point": [2, 3]}],
    "expected": {"Point": [3, 4]}
  }
]
```

## Common Custom Class Examples

### Example 1: Interval Class

```python
class Interval:
    def __init__(self, start=0, end=0):
        self.start = start
        self.end = end
    
    def __eq__(self, other):
        if not isinstance(other, Interval):
            return False
        return self.start == other.start and self.end == other.end
    
    def __repr__(self):
        return f"Interval({self.start}, {self.end})"

def list_to_interval(data):
    """Convert [start, end] to Interval"""
    if not data or len(data) != 2:
        return None
    return Interval(data[0], data[1])

register_deserializer("Interval", list_to_interval)
```

**Test Case:**
```json
{
  "input": [{"Interval": [1, 5]}],
  "expected": {"Interval": [0, 6]}
}
```

### Example 2: Matrix Class

```python
class Matrix:
    def __init__(self, grid):
        self.grid = grid
        self.rows = len(grid)
        self.cols = len(grid[0]) if grid else 0
    
    def __eq__(self, other):
        if not isinstance(other, Matrix):
            return False
        return self.grid == other.grid
    
    def __repr__(self):
        return f"Matrix({self.grid})"

def list_to_matrix(data):
    """Convert 2D list to Matrix"""
    return Matrix(data)

register_deserializer("Matrix", list_to_matrix)
```

**Test Case:**
```json
{
  "input": [{"Matrix": [[1, 2, 3], [4, 5, 6]]}],
  "expected": {"Matrix": [[1, 4], [2, 5], [3, 6]]}
}
```

### Example 3: Graph Node

```python
class GraphNode:
    def __init__(self, val=0, neighbors=None):
        self.val = val
        self.neighbors = neighbors if neighbors is not None else []
    
    def __eq__(self, other):
        if not isinstance(other, GraphNode):
            return False
        return self.val == other.val and len(self.neighbors) == len(other.neighbors)
    
    def __repr__(self):
        neighbor_vals = [n.val for n in self.neighbors]
        return f"GraphNode(val={self.val}, neighbors={neighbor_vals})"

def list_to_graphnode(data):
    """
    Convert adjacency list to GraphNode.
    Format: [[node_val, [neighbor_vals]], ...]
    Example: [[1, [2, 3]], [2, [1]], [3, [1]]]
    """
    if not data:
        return None
    
    # Create nodes first
    nodes = {}
    for node_data in data:
        val = node_data[0]
        nodes[val] = GraphNode(val)
    
    # Add neighbors
    for node_data in data:
        val, neighbor_vals = node_data
        for neighbor_val in neighbor_vals:
            if neighbor_val in nodes:
                nodes[val].neighbors.append(nodes[neighbor_val])
    
    # Return the first node
    return nodes[data[0][0]] if data else None

register_deserializer("GraphNode", list_to_graphnode)
```

**Test Case:**
```json
{
  "input": [{"GraphNode": [[1, [2, 3]], [2, [1]], [3, [1]]]}],
  "expected": {"GraphNode": [[2, []]]}
}
```

## Best Practices

### 1. Always Implement `__eq__` Method

```python
def __eq__(self, other):
    if not isinstance(other, self.__class__):
        return False
    # Compare relevant attributes
    return self.attr1 == other.attr1 and self.attr2 == other.attr2
```

### 2. Implement `__repr__` for Debugging

```python
def __repr__(self):
    return f"ClassName(attr1={self.attr1}, attr2={self.attr2})"
```

### 3. Handle Edge Cases in Deserializers

```python
def list_to_custom_class(data):
    # Handle None/empty input
    if not data:
        return None
    
    # Validate input format
    if not isinstance(data, list) or len(data) != expected_length:
        raise ValueError(f"Expected list of length {expected_length}, got {data}")
    
    # Create and return instance
    return CustomClass(data[0], data[1])
```

### 4. Use Descriptive Deserializer Names

```python
# Good
register_deserializer("Point", list_to_point)
register_deserializer("Interval", list_to_interval)

# Avoid
register_deserializer("P", some_function)
register_deserializer("Data", generic_function)
```

## Complete Working Example

Here's a complete example you can run:

```python
from pyleet import register_deserializer

class Point:
    def __init__(self, x=0, y=0):
        self.x = x
        self.y = y
    
    def __eq__(self, other):
        if not isinstance(other, Point):
            return False
        return self.x == other.x and self.y == other.y
    
    def __repr__(self):
        return f"Point({self.x}, {self.y})"

def list_to_point(data):
    if not data or len(data) != 2:
        return None
    return Point(data[0], data[1])

register_deserializer("Point", list_to_point)

class Solution:
    def distanceFromOrigin(self, point):
        if not point:
            return 0
        return (point.x ** 2 + point.y ** 2) ** 0.5
```

**Test Cases (point_testcases.json):**
```json
[
  {
    "input": [{"Point": [3, 4]}],
    "expected": 5.0
  },
  {
    "input": [{"Point": [0, 0]}],
    "expected": 0.0
  }
]
```

**Run with:**
```bash
python pyleet/cli.py your_solution.py --testcases point_testcases.json
```

## Key Points

1. **Any class can be used** - Not limited to TreeNode/ListNode
2. **Register before use** - Call `register_deserializer()` before test cases are loaded
3. **Use concise format** - `{"ClassName": data}` in JSON test cases
4. **Handle edge cases** - Deserializers should handle None, empty, and invalid input
5. **Implement comparison methods** - `__eq__` is essential for test validation

This system makes Pyleet extremely flexible for testing any type of algorithm that uses custom data structures!
