"""
Module to dynamically load and run the user's LeetCode solution
with provided test cases.
"""

import importlib.util
import sys
import os
from pyleet.datastructures import set_user_module


def run_solution(solution_path, test_cases):
    """
    Run the solution with the given test cases.

    Args:
        solution_path (str): Path to the user's solution .py file.
        test_cases (list): List of (input_args, expected_output) tuples.

    Returns:
        list of dict: Each dict contains input, expected, actual, and pass/fail status.
    """
    # Get the user's solution module (already loaded by CLI)
    module_name = "user_solution"
    if module_name in sys.modules:
        user_module = sys.modules[module_name]
    else:
        # Fallback: load the module if not already loaded
        spec = importlib.util.spec_from_file_location(
            module_name, solution_path)
        user_module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = user_module
        spec.loader.exec_module(user_module)

        # Set the user module for deserializers to access user-defined classes
        set_user_module(user_module)

    # Find the solution function or class
    solution_func = None
    if hasattr(user_module, "Solution"):
        solution_instance = user_module.Solution()
        # Use the first non-dunder method as the solution method
        for attr in dir(solution_instance):
            if not attr.startswith("__") and callable(getattr(solution_instance, attr)):
                solution_func = getattr(solution_instance, attr)
                break
    else:
        # Use the first non-dunder function in the module
        for attr in dir(user_module):
            obj = getattr(user_module, attr)
            if callable(obj) and not attr.startswith("__"):
                solution_func = obj
                break

    if solution_func is None:
        raise ValueError(
            "No solution function or class found in the provided file.")

    results = []
    # The test_cases list now contains already deserialized input_args and expected values
    for deserialized_input_args, deserialized_expected in test_cases:
        try:
            # The runner now receives fully formed objects
            actual = solution_func(*deserialized_input_args)
            passed = _compare_outputs(actual, deserialized_expected)
        except Exception as e:
            actual = f"Error: {e}"
            passed = False

        results.append({
            "input": deserialized_input_args,  # Store the deserialized input for reporting
            # Store the deserialized expected for reporting
            "expected": deserialized_expected,
            "actual": actual,
            "passed": passed
        })

    return results

# Removed _maybe_deserialize function as it's now handled in testcase_loader


def _compare_outputs(actual, expected):
    """
    Compare actual output with expected output.
    Note: This might need enhancement for complex custom objects if __eq__ is not defined.
    """
    # TODO: Consider enhancing comparison for custom objects (e.g., tree/list node structure)
    return actual == expected
