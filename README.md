# Pyleet

Pyleet is a Python tool that allows you to **run and test your LeetCode Python solutions locally** with minimal modification. It bridges the gap between LeetCode's online environment and local development, making it easier to debug and verify your solutions offline.

---

## Features

- Run LeetCode Python solutions locally without modifying your original code
- Provide test cases in a separate file (`.txt` or `.json`)
- Simple CLI command: `pyleet solution.py --testcases cases.txt`
- Supports common LeetCode data structures (lists, integers, strings)
- Easy installation via `pip` or `setup.py`
- Designed to be extensible for more data structures and features

---

## Installation

### Using pip (after packaging/uploading)

```bash
pip install pyleet
```

### From source (development mode)

```bash
git clone https://github.com/yourusername/pyleet.git
cd pyleet
pip install -e .
```

---

## Usage

Prepare your **LeetCode solution file** (e.g., `solution.py`) as you would submit it on LeetCode, without modification.

Prepare a **test case file** (e.g., `cases.txt`) containing your test inputs and expected outputs.

### Example command

```bash
pyleet solution.py --testcases cases.txt
```

### Example test case file format

Plain text format (initial version):

```
[1,2,3]
6

[4,5,6]
15
```

Each test case consists of:
- **Input arguments** (e.g., `[1,2,3]`)
- **Expected output** (e.g., `6`)
- Separated by a blank line

---

## How It Works

- Loads your solution file dynamically
- Loads and parses the test cases from the external file
- Converts inputs into Python data structures
- Calls your solution method with the inputs
- Compares the output to the expected result
- Reports pass/fail status for each test case

---

## Roadmap

- Support for more complex data structures (trees, linked lists)
- Optional feature to **fetch test cases automatically from LeetCode**
- Integration with testing frameworks (pytest, unittest)

---

## Using Custom Classes (e.g., ListNode, TreeNode, or your own)

Pyleet supports custom data structures by allowing you to provide class definitions and deserialization functions.

### 1. Include your class definitions inside your solution file

For example, include the `ListNode` or `TreeNode` class **above** your solution class or function:

```python
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right
```

### 2. Write a deserializer function for your custom class

For example, to convert a list into a `ListNode` linked list:

```python
def my_list_to_listnode(lst):
    dummy = ListNode(0)
    current = dummy
    for val in lst:
        current.next = ListNode(val)
        current = current.next
    return dummy.next
```

### 3. Register your deserializer

In your solution file, after defining the function, register it:

```python
from pyleet.datastructures import register_deserializer

register_deserializer("ListNode", my_list_to_listnode)
```

### 4. Prepare your test cases using the concise format

Write your test cases using the concise format for custom classes:

```json
[
  {
    "input": [{"ListNode": [1,2,3]}],
    "expected": {"ListNode": [1,2,3]}
  },
  {
    "input": [{"TreeNode": [1,2,3,null,null,4,5]}],
    "expected": {"TreeNode": [1,2,3,null,null,4,5]}
  }
]
```

The format is simple: `{"ClassName": data}` where:
- `ClassName` is the name of your custom class (e.g., "ListNode", "TreeNode")
- `data` is the raw data that will be passed to your deserializer function

### 5. How it works

- Pyleet detects the custom class format `{"ClassName": data}` and uses your registered deserializer to convert the data into an instance of your class.
- You can register deserializers for **any** custom class by following the same pattern.

---

## License

MIT License (or specify your license here)
