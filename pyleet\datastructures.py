"""
Utility functions to convert basic data structures into custom classes
like ListNode or TreeNode, and a registry for custom deserializers.
"""

_deserializer_registry = {}


def register_deserializer(type_name, func):
    """
    Register a deserializer function for a custom class.

    Args:
        type_name (str): Name of the target class.
        func (callable): Function that takes raw data and returns an instance.
    """
    _deserializer_registry[type_name] = func


def get_deserializer(type_name):
    """
    Get the deserializer function for a custom class.

    Args:
        type_name (str): Name of the target class.

    Returns:
        callable or None: The deserializer function, or None if not registered.
    """
    return _deserializer_registry.get(type_name)


def list_to_listnode(lst):
    """
    Convert a list to a ListNode linked list.
    Dynamically finds the ListNode class from the user's module.
    """
    if lst is None or (isinstance(lst, list) and len(lst) == 0):
        return None

    # Try to find ListNode class in the user module
    ListNode = _get_user_class("ListNode")
    if ListNode is None:
        raise ValueError(
            "ListNode class not found. Please define ListNode in your solution file.")

    dummy = ListNode(0)
    current = dummy
    for val in lst:
        current.next = ListNode(val)
        current = current.next
    return dummy.next


def list_to_tree(lst):
    """
    Convert a list to a TreeNode binary tree.
    Dynamically finds the TreeNode class from the user's module.
    """
    if not lst:
        return None

    # Try to find TreeNode class in the user module
    TreeNode = _get_user_class("TreeNode")
    if TreeNode is None:
        raise ValueError(
            "TreeNode class not found. Please define TreeNode in your solution file.")

    nodes = []
    for val in lst:
        node = TreeNode(val) if val is not None else None
        nodes.append(node)
    kids = nodes[::-1]
    root = kids.pop()
    for node in nodes:
        if node:
            if kids:
                node.left = kids.pop()
            if kids:
                node.right = kids.pop()
    return root


# Global variable to store reference to user module
_user_module = None


def set_user_module(module):
    """
    Set the user module so deserializers can access user-defined classes.
    """
    global _user_module
    _user_module = module


def _get_user_class(class_name):
    """
    Get a class from the user module by name.
    """
    global _user_module
    if _user_module is None:
        return None
    return getattr(_user_module, class_name, None)


# Register default deserializers
register_deserializer("ListNode", list_to_listnode)
register_deserializer("TreeNode", list_to_tree)

# Placeholder for ListNode and TreeNode class definitions
# The actual classes should be provided by the user inside their solution file
# Example:
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next
#
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None):
#         self.val = val
#         self.left = left
#         self.right = right
